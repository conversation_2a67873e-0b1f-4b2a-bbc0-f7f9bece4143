// index.js
const apiConfig = require('../../config/api.js')

Page({
  data: {
    recommendRecipes: [],
    categories: [
      {
        id: 1,
        name: '蔬菜',
        icon: '🥦'
      },
      {
        id: 2,
        name: '肉类',
        icon: '🍖'
      },
      {
        id: 3,
        name: '海鲜',
        icon: '🦐'
      },
      {
        id: 4,
        name: '主食',
        icon: '🍚'
      },
      {
        id: 5,
        name: '水果',
        icon: '🍎'
      },
      {
        id: 6,
        name: '调味料',
        icon: '🧂'
      },
      {
        id: 7,
        name: '豆制品',
        icon: '🌱'
      },
      {
        id: 8,
        name: '更多',
        icon: '➕'
      }
    ]
  },

  onLoad() {
    // 页面加载时请求推荐菜谱数据
    this.loadRecommendRecipes()
  },

  // 加载推荐菜谱
  loadRecommendRecipes() {
    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.searchRecipesByRandom}`,
      method: 'GET',
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('随机菜谱接口响应:', res)
        if (res.statusCode === 200 && res.data.code === 0) {
          this.setData({
            recommendRecipes: res.data.data.list || []
          })
        } else {
          console.error('获取推荐菜谱失败:', res.data)
          wx.showToast({
            title: '获取推荐菜谱失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('请求推荐菜谱失败:', err)
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
      }
    })
  },

  // 导航到拍照页面
  navigateToCamera() {
    wx.navigateTo({
      url: '/pages/camera/camera'
    })
  },

  // 导航到菜谱列表页
  navigateToRecipe() {
    wx.navigateTo({
      url: '/pages/recipe/recipe'
    })
  },

  // 导航到菜谱详情页
  navigateToDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/recipe-detail/recipe-detail?id=${id}`
    })
  },

  // 导航到分类页面
  navigateToCategory(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/recipe/recipe?category=${id}`
    })
  },

  // 搜索功能
  onSearch(e) {
    const keyword = e && e.detail && e.detail.value
    if (keyword) {
      wx.navigateTo({
        url: `/pages/recipe/recipe?keyword=${encodeURIComponent(keyword)}`
      })
    } else {
      // 如果没有输入，跳转到搜索页（空关键词的搜索模式）
      wx.navigateTo({
        url: '/pages/recipe/recipe?keyword='
      })
    }
  },

  // 跳转到收藏页面
  navigateToFavorite() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    })
  }
})
