const apiConfig = require('../../config/api.js');
const authManager = require('../../utils/auth.js');

Page({
  data: {
    historyList: [],
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false,
    total: 0
  },

  onLoad() {
    this.loadHistoryData();
  },

  // 验证登录状态和userId
  validateLoginStatus() {
    // 恢复登录状态
    authManager.restoreLoginState();

    const userId = wx.getStorageSync('userId');
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    console.log('验证登录状态:', {
      userId: userId,
      isLoggedIn: isLoggedIn,
      hasUserInfo: !!userInfo,
      hasToken: !!token,
      authManagerStatus: authManager.checkLoginStatus()
    });

    // 检查是否有完整的登录信息
    if (!userId || !isLoggedIn || !authManager.checkLoginStatus()) {
      return {
        valid: false,
        reason: '用户未登录或登录状态无效'
      };
    }

    return {
      valid: true,
      userId: userId
    };
  },

  // 加载历史记录数据
  loadHistoryData(isRefresh = false) {
    if (this.data.isLoading) return;

    // 如果是刷新，重置页码
    if (isRefresh) {
      this.setData({
        pageNum: 1,
        historyList: [],
        hasMore: true
      });
    }

    // 验证登录状态
    const loginStatus = this.validateLoginStatus();

    if (!loginStatus.valid) {
      console.log('登录状态验证失败:', loginStatus.reason);

      this.setData({
        historyList: [],
        isLoading: false
      });

      // 提示用户需要登录
      wx.showModal({
        title: '提示',
        content: '查看历史记录需要先登录，是否前往登录？',
        success: (res) => {
          if (res.confirm) {
            // 跳转到我的页面进行登录
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      return;
    }

    const userId = loginStatus.userId;

    this.setData({ isLoading: true });

    // 构造请求参数
    const requestData = {
      userId: userId, // 使用获取到的userId
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize
    };

    console.log('请求历史记录数据:', requestData);

    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.historyRecordsPage}`,
      method: 'POST',
      data: requestData,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('历史记录响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 0) {
          const responseData = res.data.data;
          const newList = responseData.list || [];

          // 处理数据格式，适配页面显示
          const formattedList = newList.map(item => ({
            id: item.id,
            name: item.name,
            confidence: parseFloat(item.rate.replace('%', '')), // 去掉%号转为数字
            time: item.createTime
          }));

          // 合并数据
          const currentList = isRefresh ? [] : this.data.historyList;
          const updatedList = [...currentList, ...formattedList];

          this.setData({
            historyList: updatedList,
            total: responseData.total || 0,
            hasMore: responseData.hasNext || false,
            pageNum: this.data.pageNum + 1,
            isLoading: false
          });

          console.log('历史记录加载成功:', {
            当前页码: this.data.pageNum,
            本次加载数量: formattedList.length,
            总数量: updatedList.length,
            总记录数: responseData.total,
            是否还有更多: responseData.hasNext
          });
        } else {
          console.error('获取历史记录失败:', res.data);

          // 检查是否是userId错误
          if (res.statusCode === 400 && res.data && res.data.msg && res.data.msg.includes('userId错误')) {
            console.log('userId错误，可能需要重新登录');
            wx.showModal({
              title: '登录状态异常',
              content: '您的登录状态已过期，请重新登录后查看历史记录',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 清除登录状态
                  wx.removeStorageSync('userId');
                  wx.removeStorageSync('isLoggedIn');
                  wx.removeStorageSync('userInfo');
                  wx.removeStorageSync('token');

                  // 跳转到登录页面
                  wx.switchTab({
                    url: '/pages/profile/profile'
                  });
                }
              }
            });
          } else {
            wx.showToast({
              title: res.data && res.data.msg ? res.data.msg : '获取历史记录失败',
              icon: 'none'
            });
          }

          this.setData({ isLoading: false });
        }
      },
      fail: (error) => {
        console.error('请求历史记录失败:', error);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      }
    });
  },

  // 点击历史记录项
  viewDetail(e) {
    const id = e.currentTarget.dataset.id;
    console.log('点击历史记录，ID:', id);

    if (!id) {
      wx.showToast({
        title: '数据错误',
        icon: 'none'
      });
      return;
    }

    // 根据历史记录的食材名称搜索相关菜谱
    const item = this.data.historyList.find(item => item.id === id);
    if (item && item.name) {
      // 跳转到搜索食谱页面，传递食材名称
      wx.navigateTo({
        url: `/pages/recipe/recipe?keyword=${encodeURIComponent(item.name)}`,
        fail: (error) => {
          console.error('页面跳转失败:', error);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '数据错误',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新历史记录');
    this.loadHistoryData(true);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    console.log('=== 触发上拉加载更多 ===');
    console.log('当前状态:', {
      hasMore: this.data.hasMore,
      isLoading: this.data.isLoading,
      currentPage: this.data.pageNum,
      listLength: this.data.historyList.length,
      total: this.data.total
    });

    if (this.data.hasMore && !this.data.isLoading) {
      console.log('开始加载下一页数据...');
      this.loadHistoryData();
    } else if (!this.data.hasMore) {
      console.log('没有更多数据了');
      wx.showToast({
        title: '没有更多了',
        icon: 'none',
        duration: 1000
      });
    } else if (this.data.isLoading) {
      console.log('正在加载中，忽略重复请求');
    }
  },

  // 页面显示时刷新数据
  onShow() {
    // 如果列表为空，重新加载数据
    if (this.data.historyList.length === 0) {
      this.loadHistoryData(true);
    }
  },

  // 调试登录状态
  debugLoginStatus() {
    console.log('=== 调试登录状态 ===');

    // 获取所有相关的存储数据
    const userId = wx.getStorageSync('userId');
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const openId = wx.getStorageSync('openId');
    const sessionKey = wx.getStorageSync('sessionKey');

    // 检查authManager状态
    const authStatus = authManager.checkLoginStatus();
    const currentUser = authManager.getCurrentUser();

    const debugInfo = {
      '本地存储-userId': userId,
      '本地存储-isLoggedIn': isLoggedIn,
      '本地存储-userInfo': userInfo,
      '本地存储-token': token ? '已设置' : '未设置',
      '本地存储-openId': openId,
      '本地存储-sessionKey': sessionKey ? '已设置' : '未设置',
      'authManager状态': authStatus,
      'authManager用户': currentUser
    };

    console.log('调试信息:', debugInfo);

    // 显示调试信息
    const debugText = Object.entries(debugInfo)
      .map(([key, value]) => `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`)
      .join('\n');

    wx.showModal({
      title: '登录状态调试',
      content: debugText,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 测试加载更多功能
  testLoadMore() {
    console.log('=== 手动测试加载更多 ===');
    this.onReachBottom();
  }
});